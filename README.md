# AI Assistant Memory System

A Python project for managing AI assistant memory using Supabase and OpenAI embeddings with vector similarity search.

## Features

- **Memory Injection**: Store facts with semantic embeddings
- **Similarity Search**: Retrieve relevant memories using vector similarity
- **Async Operations**: Optimized for performance with async/await
- **Environment Configuration**: Secure API key management
- **Error Handling**: Robust error handling and validation

## Setup

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Environment Configuration

Copy `.env.example` to `.env` and fill in your API keys:

```bash
cp .env.example .env
```

Edit `.env` with your credentials:
- `OPENAI_API_KEY`: Your OpenAI API key
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_KEY`: Your Supabase anon key

### 3. Database Setup

Create a `memory` table in your Supabase database:

```sql
-- Enable the pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Create the memory table
CREATE TABLE memory (
    id BIGSERIAL PRIMARY KEY,
    content TEXT NOT NULL,
    embedding VECTOR(1536) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create an index for vector similarity search
CREATE INDEX ON memory USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);

-- Optional: Create a function for similarity search
CREATE OR REPLACE FUNCTION match_memories(
    query_embedding VECTOR(1536),
    match_threshold FLOAT DEFAULT 0.1,
    match_count INT DEFAULT 3
)
RETURNS TABLE(
    id BIGINT,
    content TEXT,
    created_at TIMESTAMPTZ,
    similarity FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        memory.id,
        memory.content,
        memory.created_at,
        1 - (memory.embedding <=> query_embedding) AS similarity
    FROM memory
    WHERE 1 - (memory.embedding <=> query_embedding) > match_threshold
    ORDER BY memory.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;
```

## Usage

### Inject Memory

Store a new memory with automatic embedding generation:

```bash
python inject_memory.py "The user prefers dark mode interfaces"
python inject_memory.py "User is working on a Python project using FastAPI"
```

### Retrieve Similar Memories

Search for memories similar to a query:

```bash
python retrieve_similar.py "interface preferences"
python retrieve_similar.py "Python development"
```

## Project Structure

```
.
├── requirements.txt          # Python dependencies
├── .env.example             # Environment variables template
├── config.py                # Configuration management
├── inject_memory.py         # Memory injection script
├── retrieve_similar.py      # Memory retrieval script
└── README.md               # This file
```

## Technical Details

- **Embedding Model**: OpenAI's `text-embedding-3-small` (1536 dimensions)
- **Vector Database**: Supabase with pgvector extension
- **Similarity Metric**: Cosine similarity
- **Performance**: Async operations for optimal throughput

## Error Handling

The system includes comprehensive error handling for:
- Missing environment variables
- API connection failures
- Invalid input validation
- Database operation errors

## Security

- API keys stored in environment variables
- No hardcoded credentials
- Input validation and sanitization
