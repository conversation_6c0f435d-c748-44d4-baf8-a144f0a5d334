#!/usr/bin/env python3
"""Memory injection script for AI assistant memory system."""

import asyncio
import sys
from datetime import datetime
from typing import List

from openai import Async<PERSON>penA<PERSON>
from supabase import create_client, Client
from config import config


class MemoryInjector:
    """Handles memory injection with embedding generation."""
    
    def __init__(self) -> None:
        """Initialize OpenAI and Supabase clients."""
        self.openai_client = AsyncOpenAI(api_key=config.openai_api_key)
        self.supabase: Client = create_client(config.supabase_url, config.supabase_key)
    
    async def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for given text using OpenAI."""
        try:
            response = await self.openai_client.embeddings.create(
                model=config.embedding_model,
                input=text.strip(),
                encoding_format="float"
            )
            return response.data[0].embedding
        except Exception as e:
            raise RuntimeError(f"Failed to generate embedding: {e}")
    
    def store_memory(self, memory_text: str, embedding: List[float]) -> dict:
        """Store memory and embedding in Supabase."""
        try:
            result = self.supabase.table(config.memory_table).insert({
                "content": memory_text,
                "embedding": embedding,
                "created_at": datetime.utcnow().isoformat()
            }).execute()
            
            if result.data:
                return result.data[0]
            else:
                raise RuntimeError("No data returned from insert operation")
                
        except Exception as e:
            raise RuntimeError(f"Failed to store memory: {e}")
    
    async def inject_memory(self, memory_text: str) -> dict:
        """Complete memory injection pipeline."""
        if not memory_text.strip():
            raise ValueError("Memory text cannot be empty")
        
        print(f"Generating embedding for: {memory_text[:50]}...")
        embedding = await self.generate_embedding(memory_text)
        
        print("Storing memory in database...")
        result = self.store_memory(memory_text, embedding)
        
        print(f"✅ Memory stored successfully with ID: {result.get('id')}")
        return result


async def main() -> None:
    """Main function to handle command line input."""
    if len(sys.argv) < 2:
        print("Usage: python inject_memory.py <memory_text>")
        print("Example: python inject_memory.py 'The user prefers dark mode interfaces'")
        sys.exit(1)
    
    memory_text = " ".join(sys.argv[1:])
    
    try:
        injector = MemoryInjector()
        await injector.inject_memory(memory_text)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
