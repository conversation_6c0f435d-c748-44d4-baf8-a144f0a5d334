#!/usr/bin/env python3
"""Memory retrieval script for AI assistant memory system."""

import asyncio
import ast
import sys
from typing import List, Dict, Any

import numpy as np
from openai import AsyncOpenAI
from supabase import create_client, Client
from config import config


class MemoryRetriever:
    """Handles memory retrieval with similarity search."""

    def __init__(self) -> None:
        """Initialize OpenAI and Supabase clients."""
        self.openai_client = AsyncOpenAI(api_key=config.openai_api_key)
        self.supabase: Client = create_client(config.supabase_url, config.supabase_key)

    async def generate_query_embedding(self, query: str) -> List[float]:
        """Generate embedding for search query."""
        try:
            response = await self.openai_client.embeddings.create(
                model=config.embedding_model,
                input=query.strip(),
                encoding_format="float"
            )
            return response.data[0].embedding
        except Exception as e:
            raise RuntimeError(f"Failed to generate query embedding: {e}")

    def search_similar_memories(self, query_embedding: List[float], limit: int = 3) -> List[Dict[str, Any]]:
        """Search for similar memories using vector similarity."""
        try:
            # Simple approach: get all memories and calculate similarity in Python
            result = self.supabase.table(config.memory_table).select(
                "id, content, created_at, embedding"
            ).execute()

            if not result.data:
                return []

            # Calculate cosine similarity in Python

            memories_with_similarity = []
            query_vec = np.array(query_embedding, dtype=np.float32)

            for memory in result.data:
                # Handle embedding format - could be string or list
                embedding_data = memory['embedding']
                if isinstance(embedding_data, str):
                    # Parse string representation of list
                    embedding_data = ast.literal_eval(embedding_data)

                memory_vec = np.array(embedding_data, dtype=np.float32)
                # Cosine similarity
                similarity = np.dot(query_vec, memory_vec) / (np.linalg.norm(query_vec) * np.linalg.norm(memory_vec))

                memories_with_similarity.append({
                    'id': memory['id'],
                    'content': memory['content'],
                    'created_at': memory['created_at'],
                    'similarity': float(similarity)
                })

            # Sort by similarity (highest first) and return top results
            memories_with_similarity.sort(key=lambda x: x['similarity'], reverse=True)
            return memories_with_similarity[:limit]

        except Exception as e:
            raise RuntimeError(f"Failed to search memories: {e}")

    async def retrieve_similar(self, query: str, limit: int = 3) -> List[Dict[str, Any]]:
        """Complete memory retrieval pipeline."""
        if not query.strip():
            raise ValueError("Query cannot be empty")

        print(f"Searching for memories similar to: {query}")

        # Generate embedding for query
        query_embedding = await self.generate_query_embedding(query)

        # Search for similar memories
        similar_memories = self.search_similar_memories(query_embedding, limit)

        if not similar_memories:
            print("No similar memories found.")
            return []

        print(f"\n🔍 Found {len(similar_memories)} similar memories:")
        print("-" * 60)

        for i, memory in enumerate(similar_memories, 1):
            similarity_score = memory.get('similarity', 'N/A')
            content = memory.get('content', 'No content')
            created_at = memory.get('created_at', 'Unknown date')

            print(f"{i}. Similarity: {similarity_score}")
            print(f"   Content: {content}")
            print(f"   Created: {created_at}")
            print("-" * 60)

        return similar_memories


async def main() -> None:
    """Main function to handle command line input."""
    if len(sys.argv) < 2:
        print("Usage: python retrieve_similar.py <query>")
        print("Example: python retrieve_similar.py 'user interface preferences'")
        sys.exit(1)

    query = " ".join(sys.argv[1:])

    try:
        retriever = MemoryRetriever()
        await retriever.retrieve_similar(query)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
